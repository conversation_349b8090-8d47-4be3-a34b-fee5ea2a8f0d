package main

import (
	"fmt"
	"qwen-solve/internal/model"
	"qwen-solve/pkg/utils"
)

func main() {
	fmt.Println("=== 测试 hashRaw 字段标点符号清洗 ===")
	
	// 测试数据：包含各种标点符号的题目
	testCases := []struct {
		name         string
		questType    model.QuestionType
		questContent string
		questOptions map[string]string
	}{
		{
			name:         "带标点符号的单选题",
			questType:    model.QuestionTypeSingle,
			questContent: "这是一个测试题目：请选择正确答案？",
			questOptions: map[string]string{
				"A": "选项A：答案A，正确答案！",
				"B": "选项B：答案B；错误答案。",
				"C": "选项C：答案C？可能答案...",
				"D": "选项D：答案D（最终答案）",
			},
		},
		{
			name:         "带空格换行的多选题",
			questType:    model.QuestionTypeMultiple,
			questContent: "这是一个测试题目\n请选择正确答案\t多个选项",
			questOptions: map[string]string{
				"A": "选项 A 答案 A",
				"B": "选项\tB\t答案\tB",
				"C": "选项\nC\n答案\nC",
				"D": "选项　D　答案　D", // 包含全角空格
			},
		},
		{
			name:         "带各种标点的判断题",
			questType:    model.QuestionTypeJudge,
			questContent: "这是判断题！@#$%^&*()对吗？？？",
			questOptions: map[string]string{
				"Y": "正确：：：是的！！！",
				"N": "错误。。。不是；；；",
			},
		},
	}
	
	for i, testCase := range testCases {
		fmt.Printf("\n--- 测试案例 %d: %s ---\n", i+1, testCase.name)
		
		// 生成缓存键
		hashKey, hashRaw := utils.GenerateCacheKey(
			testCase.questType,
			testCase.questContent,
			testCase.questOptions,
		)
		
		fmt.Printf("题目类型: %s\n", testCase.questType)
		fmt.Printf("题目内容: %s\n", testCase.questContent)
		fmt.Printf("题目选项: %+v\n", testCase.questOptions)
		fmt.Printf("HashRaw (清洗后): %s\n", hashRaw)
		fmt.Printf("HashKey (MD5): %s\n", hashKey)
		
		// 验证 hashRaw 是否已清洗
		fmt.Printf("HashRaw长度: %d\n", len(hashRaw))
		
		// 检查是否还包含常见标点符号
		punctuations := []string{"：", "？", "！", "，", "。", "；", " ", "\t", "\n", "\r", "　"}
		hasPunctuation := false
		for _, punct := range punctuations {
			if contains(hashRaw, punct) {
				fmt.Printf("⚠️  HashRaw仍包含标点符号: '%s'\n", punct)
				hasPunctuation = true
			}
		}
		
		if !hasPunctuation {
			fmt.Printf("✅ HashRaw已成功清洗所有标点符号\n")
		}
	}
	
	fmt.Println("\n=== 验证相同内容不同标点的一致性 ===")
	
	// 测试相同内容不同标点符号
	options := map[string]string{
		"A": "答案A",
		"B": "答案B", 
		"C": "答案C",
		"D": "答案D",
	}
	
	hashKey1, hashRaw1 := utils.GenerateCacheKey(
		model.QuestionTypeSingle,
		"这是测试题目：请选择答案？",
		options,
	)
	
	hashKey2, hashRaw2 := utils.GenerateCacheKey(
		model.QuestionTypeSingle,
		"这是测试题目请选择答案",
		options,
	)
	
	fmt.Printf("内容1 HashRaw: %s\n", hashRaw1)
	fmt.Printf("内容1 HashKey: %s\n", hashKey1)
	fmt.Printf("内容2 HashRaw: %s\n", hashRaw2)
	fmt.Printf("内容2 HashKey: %s\n", hashKey2)
	fmt.Printf("HashRaw是否一致: %t\n", hashRaw1 == hashRaw2)
	fmt.Printf("HashKey是否一致: %t\n", hashKey1 == hashKey2)
	
	if hashRaw1 == hashRaw2 && hashKey1 == hashKey2 {
		fmt.Println("✅ 标点符号清洗功能正常，缓存一致性得到保证！")
	} else {
		fmt.Println("❌ 标点符号清洗功能存在问题！")
	}
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
