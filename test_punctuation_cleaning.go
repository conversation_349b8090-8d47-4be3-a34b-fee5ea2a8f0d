package main

import (
	"encoding/json"
	"fmt"
	"qwen-solve/pkg/utils"
)

func main() {
	fmt.Println("=== 测试标点符号清洗功能 ===")
	
	// 测试标点符号清洗
	results := utils.TestPunctuationCleaning()
	
	// 格式化输出结果
	jsonData, err := json.MarshalIndent(results, "", "  ")
	if err != nil {
		fmt.Printf("JSON序列化失败: %v\n", err)
		return
	}
	
	fmt.Println(string(jsonData))
	
	fmt.Println("\n=== 手动测试特殊情况 ===")
	
	// 手动测试一些特殊情况
	testCases := []string{
		"单选题：这是一个测试题目？选项A：答案A，选项B：答案B！",
		"单选题这是一个测试题目选项A答案A选项B答案B",
		"单选题！@#$%^&*()这是一个测试题目？？？选项A：：：答案A，，，选项B！！！答案B",
		"单选题，。；：！？这是测试",
		"单选题\n\r\t 这是测试　全角空格",
	}
	
	for i, testCase := range testCases {
		cleaned := utils.CleanTextForHash(testCase)
		hash := utils.GenerateMD5(cleaned)
		
		fmt.Printf("测试案例 %d:\n", i+1)
		fmt.Printf("  原文: %s\n", testCase)
		fmt.Printf("  清洗后: %s\n", cleaned)
		fmt.Printf("  哈希值: %s\n", hash)
		fmt.Printf("  长度变化: %d -> %d\n\n", len(testCase), len(cleaned))
	}
	
	fmt.Println("=== 验证相同内容不同标点的哈希一致性 ===")
	
	// 测试相同内容不同标点符号的情况
	content1 := "单选题：这是一个测试题目？选项A：答案A，选项B：答案B！选项C：答案C。选项D：答案D；"
	content2 := "单选题这是一个测试题目选项A答案A选项B答案B选项C答案C选项D答案D"
	content3 := "单选题！！！这是一个测试题目？？？选项A：：：答案A，，，选项B！！！答案B。。。选项C；；；答案C选项D答案D"
	
	hash1 := utils.GenerateMD5(utils.CleanTextForHash(content1))
	hash2 := utils.GenerateMD5(utils.CleanTextForHash(content2))
	hash3 := utils.GenerateMD5(utils.CleanTextForHash(content3))
	
	fmt.Printf("内容1哈希: %s\n", hash1)
	fmt.Printf("内容2哈希: %s\n", hash2)
	fmt.Printf("内容3哈希: %s\n", hash3)
	fmt.Printf("哈希是否一致: %t\n", hash1 == hash2 && hash2 == hash3)
	
	if hash1 == hash2 && hash2 == hash3 {
		fmt.Println("✅ 标点符号清洗功能正常工作！")
	} else {
		fmt.Println("❌ 标点符号清洗功能存在问题！")
	}
}
